include(GenerateExportHeader)

##设置target名称
set(TARGET_NAME SampleShared)

##设置源代码目录
set(SOURCE_DIR ${CMAKE_CURRENT_SOURCE_DIR})
set(CMAKE_AUTOMOC ON)

add_definitions(-DQT_COMMON_WIDGET_HOME -DQT_DISABLE_DEPRECATED_BEFORE=0 -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_DLL -DQT_CORE_LIB -DQT_NO_OPENGL -DQT_NO_DEBUG -DVLD_CHECK -DNOMINMAX) 

# 提取所有的源文件(头文件和源文件分开提取)
file(GLOB_RECURSE  HEADER_FILES
    LIST_DIRECTORIES False  CONFIGURE_DEPENDS
    "${SOURCE_DIR}/*.h*"
)
file(GLOB_RECURSE  SRC_FILES
    LIST_DIRECTORIES False  CONFIGURE_DEPENDS
    "${SOURCE_DIR}/*.c*"
    "${SOURCE_DIR}/*.ui"
)

# 为VS设置源代码文件夹
source_group(
    TREE "${SOURCE_DIR}"
    PREFIX "Header Files"
    FILES ${HEADER_FILES}
)
source_group(
    TREE "${SOURCE_DIR}"
    PREFIX "Source Files"
    FILES ${SRC_FILES}
)

##界面资源
file(GLOB FORM_SOURCE_FILES QtGui/*.ui) 
source_group("Forms" FILES ${FORM_SOURCE_FILES}) 

add_library(${TARGET_NAME} SHARED)

# 创建动态库的符号导出头文件
#generate_export_header(${TARGET_NAME}
#    BASE_NAME "${TARGET_NAME}"
#    EXPORT_FILE_NAME "${CMAKE_CURRENT_SOURCE_DIR}/${TARGET_NAME}.h"
#)

find_package(Qt5 COMPONENTS Core Gui  Qml Quick Widgets REQUIRED)
qt5_add_resources(QRC_FILES 
    "${CMAKE_CURRENT_SOURCE_DIR}/Resource.qrc"
)

##界面转换
set_target_properties(${TARGET_NAME} PROPERTIES
    AUTOUIC ON 
)
set(CMAKE_AUTOUIC ON)

# 指定源文件
target_sources(${TARGET_NAME}
    PRIVATE  ${SRC_FILES}
    PRIVATE  ${HEADER_FILES}
    PRIVATE  ${QRC_FILES}
)

# 设置预处理器定义
if(MSVC)
    target_compile_definitions(${TARGET_NAME}
        PRIVATE UNICODE NOMINMAX
    )
endif()

# 配置构建/使用时的头文件路径
target_include_directories(${TARGET_NAME}
    PUBLIC $<BUILD_INTERFACE:${SOURCE_DIR}> $<BUILD_INTERFACE:${SOURCE_DIR}>/UIConfig
	${GbmpThirdPart_pugixml_Include}
	${GbmpThirdPart_GCMPSDK_include_dotnetapi}
)

##设置所在文件夹
set_target_properties(${TARGET_NAME} PROPERTIES
    FOLDER "示例程序框架"
)

##设置库依赖
target_link_libraries(${TARGET_NAME}
    PRIVATE Gcmp::All
            Qt5::Core
            Qt5::Gui
            Qt5::Qml
            Qt5::Quick 
            Qt5::Widgets
)

add_custom_command(
    TARGET ${TARGET_NAME} POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E chdir ${SOURCE_DIR}
            cmd /c "copy.bat \"${CMAKE_SOURCE_DIR}\" \"${SOURCE_DIR}\" \"$(Platform)\" \"$(Configuration)\" \"$(TargetDir)\" \"${IN_GDMPLAB}\""
    COMMENT "Copying SampleShared dependencies and configuration files"
    VERBATIM
)