﻿#This file is generated by J<PERSON>.

cmake_minimum_required(VERSION 3.8)

# 设置处理 GENERATED 文件的策略
if(POLICY CMP0071)
    cmake_policy(SET CMP0071 NEW)
endif()

# 设置其他策略以提高兼容性
if(POLICY CMP0074)
    cmake_policy(SET CMP0074 NEW)
endif()

if(POLICY CMP0077)
    cmake_policy(SET CMP0077 NEW)
endif()

project(GDMPLab)

# 检测编译器版本并设置兼容性标志
if(MSVC)
    if(MSVC_VERSION GREATER_EQUAL 1930)
        message(STATUS "Detected Visual Studio 2022 (MSVC ${MSVC_VERSION})")
        set(GDMP_COMPILER_VS2022 ON)
    elseif(MSVC_VERSION GREATER_EQUAL 1920)
        message(STATUS "Detected Visual Studio 2019 (MSVC ${MSVC_VERSION})")
        set(GDMP_COMPILER_VS2019 ON)
    elseif(MSVC_VERSION GREATER_EQUAL 1910)
        message(STATUS "Detected Visual Studio 2017 (MSVC ${MSVC_VERSION})")
        set(GDMP_COMPILER_VS2017 ON)
    else()
        message(STATUS "Detected older Visual Studio version (MSVC ${MSVC_VERSION})")
    endif()
endif()

set(CMAKE_CONFIGURATION_TYPES "Release")
#set(CMAKE_CONFIGURATION_TYPES "Debug;Release")
set(CMAKE_CXX_FLAGS "/DWIN64 /D_WINDOWS /D_USRDLL /Zi /nologo /W3 /WX /MP  /EHsc -DUNICODE -D_UNICODE -D_CRT_SECURE_NO_WARNINGS /wd4250 /wd4251 /wd4996")
set(CMAKE_CXX_FLAGS_RELEASE "/O2 /MD /DNDEBUG")
set(CMAKE_CXX_FLAGS_DEBUG "/Od /MDd /D_DEBUG")
set(CMAKE_SHARED_LINKER_FLAGS_RELEASE "${CMAKE_SHARED_LINKER_FLAGS_RELEASE} /DEBUG /INCREMENTAL")
set(CMAKE_EXE_LINKER_FLAGS_RELEASE "${CMAKE_EXE_LINKER_FLAGS_RELEASE} /DEBUG /INCREMENTAL")
set(CMAKE_SHARED_LINKER_FLAGS_DEBUG "${CMAKE_SHARED_LINKER_FLAGS_DEBUG} /DEBUG")
set(CMAKE_EXE_LINKER_FLAGS_DEBUG "${CMAKE_EXE_LINKER_FLAGS_DEBUG} /DEBUG")


set(Platform "$(Platform)")
set(Configuration "$(Configuration)")
set(ProjectName "$(ProjectName)")

set(IN_GDMPLAB ON)
add_definitions(-DIS_IN_GDMPLAB)

# 定义IN_LAB变量：当IS_IN_GDMPLAB定义时为1，否则为0
if(IN_GDMPLAB)
    add_definitions(-DIN_LAB=1)
else()
    add_definitions(-DIN_LAB=0)
endif()

set(GbmpDir ${CMAKE_CURRENT_SOURCE_DIR}/../)
set(GbmpThirdPart ${GbmpDir}/thirdparty)
set(GbmpThirdPart_GCMPSDK ${GbmpThirdPart}/GCMP_SDK)
set(GbmpThirdPart_tcmalloc_lib_x64 ${GbmpThirdPart}/tcmalloc/x64)
set(GbmpThirdPart_GCMPSDK_include ${GbmpThirdPart}/GCMP_SDK/include)
set(GbmpThirdPart_GCMPSDK_include_gnuf ${GbmpThirdPart}/GCMP_SDK/include/Gnuf)
set(GbmpThirdPart_GCMPSDK_include_dotnetapi ${GbmpThirdPart}/GCMP_SDK/include/DotnetApi/GcmpExternalAddInInterface)
set(GbmpThirdPart_GCMPSDK_lib ${GbmpThirdPart}/GCMP_SDK/lib/${Configuration})
set(GbmpThirdPart_pugixml_Include ${GbmpThirdPart}/pugixml/include)
set(GbmpThirdPart_jsoncpp_Include ${GbmpThirdPart}/jsoncpp/include)
#set(GbmpThirdPart_elementlocator ${GbmpThirdPart}/GDMPElementLocator)//为了兼容0827GDMP_Components中的罗盘暂注释掉
#set(GbmpThirdPart_elementlocator_Include ${GbmpThirdPart}/GDMPElementLocator/include)
#set(GbmpThirdPart_elementlocator_lib ${GbmpThirdPart}/GDMPElementLocator/lib/${Configuration})

set(GDMPComponentsSDK_DIR ${GbmpThirdPart}/GDMP_Components)

# 添加为全局预处理器定义
add_definitions(-DIS_IN_GDMPLAB=${ON})

set(QTDIR_X64 $ENV{QTDIR_X64})
set(QTDIR_X64_include $ENV{QTDIR_X64}/include)
set(QTDIR_X64_include_ActiveQt $ENV{QTDIR_X64}/include/ActiveQt)
set(QTDIR_X64_include_QtCore $ENV{QTDIR_X64}/include/QtCore)
set(QTDIR_X64_include_QtNetwork $ENV{QTDIR_X64}/include/QtNetwork)
set(QTDIR_X64_include_QtWidgets $ENV{QTDIR_X64}/include/QtWidgets)
set(QTDIR_X64_include_QtGui $ENV{QTDIR_X64}/include/QtGui)
set(QTDIR_X64_include_QtXml $ENV{QTDIR_X64}/include/QtXml)
set(QTDIR_X64_include_QtScript $ENV{QTDIR_X64}/include/QtScript)
set(QTDIR_X64_include_QtPrintSupport $ENV{QTDIR_X64}/include/QtPrintSupport)
set(QTDIR_X64_QtScriptTools $ENV{QTDIR_X64}/include/QtScriptTools)
set(QTDIR_X64_include_QtScriptTools $ENV{QTDIR_X64}/include/QtScriptTools)
set(QTDIR_X64_include_QtSql $ENV{QTDIR_X64}/include/QtSql)
set(QTDIR_X64_include_QtWidgets_5_1_1_QtWidgets $ENV{QTDIR_X64}/include/QtWidgets/5.1.1/QtWidgets)
set(QTDIR_X64_include_QtCore_5_1_1_QtCore ${QTDIR_X64}/include/QtCore/5.1.1/QtCore)
set(QTDIR_X64_mkspecs_win32_msvc2015 ${QTDIR_X64}/mkspecs/win32-msvc2015)
set(QTDIR_X64_lib $ENV{QTDIR_X64}/lib)
set(CMAKE_PREFIX_PATH ${QTDIR_X64}) 

set(AppGdmpLab ${CMAKE_CURRENT_SOURCE_DIR}/GdmpLab/AppGdmpLab)
set(AppGdmpLabImpl ${CMAKE_CURRENT_SOURCE_DIR}/GdmpLab/AppGdmpLabImpl)
set(UiPlatform ${CMAKE_CURRENT_SOURCE_DIR}/GdmpLab/Interaction/UiPlatform)
set(UiConfiguration ${CMAKE_CURRENT_SOURCE_DIR}/GdmpLab/Gui/UiConfiguration)
set(QtGuiImplementation ${CMAKE_CURRENT_SOURCE_DIR}/GdmpLab/Gui/QtGuiImplementation)
set(UiCommonComponent ${CMAKE_CURRENT_SOURCE_DIR}/GdmpLab/Gui/UiCommonComponent)
set(GUiBaseImpl ${CMAKE_CURRENT_SOURCE_DIR}/GdmpLab/Gui/GUiBaseImpl)
set(GuiMainFrame ${CMAKE_CURRENT_SOURCE_DIR}/GdmpLab/Gui/GuiMainFrame)
set(GuiQt ${CMAKE_CURRENT_SOURCE_DIR}/GdmpLab/Gui/GuiQt)
set(UiQtImpl ${CMAKE_CURRENT_SOURCE_DIR}/GdmpLab/Gui/UiQtImpl)
set(UiDataExchange ${CMAKE_CURRENT_SOURCE_DIR}/GdmpLab/UiDataExchange)
set(UiInplaceEdit ${CMAKE_CURRENT_SOURCE_DIR}/GdmpLab/Interaction/UiInplaceEdit)
set(QtCommonWidget ${CMAKE_CURRENT_SOURCE_DIR}/GdmpLab/Gui/QtCommonWidget)
set(Model ${CMAKE_CURRENT_SOURCE_DIR}/GdmpLab/Db/Model)

link_directories(${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration))
link_directories(${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration)/sdk)

add_subdirectory(ThirdPartyUpdate)
add_subdirectory(GdmpLab/AppGdmpLab)
add_subdirectory(GdmpLab/AppGdmpLabImpl)
add_subdirectory(GdmpLab/Interaction/UiPlatform)
add_subdirectory(GdmpLab/Gui/UiConfiguration)
add_subdirectory(GdmpLab/Gui/QtGuiImplementation)
add_subdirectory(GdmpLab/Gui/UiCommonComponent)
add_subdirectory(GdmpLab/Gui/GUiBaseImpl)
add_subdirectory(GdmpLab/Gui/GuiMainFrame)
add_subdirectory(GdmpLab/Gui/GuiQt)
add_subdirectory(GdmpLab/Gui/UiQtImpl)
add_subdirectory(GdmpLab/Interaction/UiInplaceEdit)
add_subdirectory(GdmpLab/Gui/QtCommonWidget)
add_subdirectory(GdmpLab/Db/Model)
add_subdirectory(Addins/CppSamples)
add_subdirectory(Addins/Components)
add_subdirectory(Addins/Structure)

foreach( OUTPUTCONFIG ${CMAKE_CONFIGURATION_TYPES} )
string( TOUPPER ${OUTPUTCONFIG} OUTPUTCONFIG )
set_target_properties(AppGdmpLab PROPERTIES   RUNTIME_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) )
set_target_properties(AppGdmpLab PROPERTIES   LIBRARY_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) )
set_target_properties(AppGdmpLab PROPERTIES   ARCHIVE_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) )
set_target_properties(AppGdmpLabImpl PROPERTIES   RUNTIME_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) )
set_target_properties(AppGdmpLabImpl PROPERTIES   LIBRARY_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) )
set_target_properties(AppGdmpLabImpl PROPERTIES   ARCHIVE_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) )
set_target_properties(UiPlatform PROPERTIES   RUNTIME_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) )
set_target_properties(UiPlatform PROPERTIES   LIBRARY_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) )
set_target_properties(UiPlatform PROPERTIES   ARCHIVE_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) )
set_target_properties(UiConfiguration PROPERTIES   RUNTIME_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) )
set_target_properties(UiConfiguration PROPERTIES   LIBRARY_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) )
set_target_properties(UiConfiguration PROPERTIES   ARCHIVE_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) )
set_target_properties(QtGuiImplementation PROPERTIES   RUNTIME_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) )
set_target_properties(QtGuiImplementation PROPERTIES   LIBRARY_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) )
set_target_properties(QtGuiImplementation PROPERTIES   ARCHIVE_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) )
set_target_properties(UiCommonComponent PROPERTIES   RUNTIME_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) )
set_target_properties(UiCommonComponent PROPERTIES   LIBRARY_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) )
set_target_properties(UiCommonComponent PROPERTIES   ARCHIVE_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) )
set_target_properties(GUiBaseImpl PROPERTIES   RUNTIME_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) )
set_target_properties(GUiBaseImpl PROPERTIES   LIBRARY_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) )
set_target_properties(GUiBaseImpl PROPERTIES   ARCHIVE_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) )
set_target_properties(GuiMainFrame PROPERTIES   RUNTIME_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) )
set_target_properties(GuiMainFrame PROPERTIES   LIBRARY_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) )
set_target_properties(GuiMainFrame PROPERTIES   ARCHIVE_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) )
set_target_properties(GuiQt PROPERTIES   RUNTIME_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) )
set_target_properties(GuiQt PROPERTIES   LIBRARY_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) )
set_target_properties(GuiQt PROPERTIES   ARCHIVE_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) )
set_target_properties(UiQtImpl PROPERTIES   RUNTIME_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) )
set_target_properties(UiQtImpl PROPERTIES   LIBRARY_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) )
set_target_properties(UiQtImpl PROPERTIES   ARCHIVE_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) )
set_target_properties(UiInplaceEdit PROPERTIES   RUNTIME_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) )
set_target_properties(UiInplaceEdit PROPERTIES   LIBRARY_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) )
set_target_properties(UiInplaceEdit PROPERTIES   ARCHIVE_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) )
set_target_properties(QtCommonWidget PROPERTIES   RUNTIME_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) )
set_target_properties(QtCommonWidget PROPERTIES   LIBRARY_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) )
set_target_properties(QtCommonWidget PROPERTIES   ARCHIVE_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) )
set_target_properties(Model PROPERTIES   RUNTIME_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) )
set_target_properties(Model PROPERTIES   LIBRARY_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) )
set_target_properties(Model PROPERTIES   ARCHIVE_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) )

set_target_properties(ThirdPartyUpdate PROPERTIES   RUNTIME_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) )
set_target_properties(ThirdPartyUpdate PROPERTIES   LIBRARY_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) )
set_target_properties(ThirdPartyUpdate PROPERTIES   ARCHIVE_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) )
endforeach( OUTPUTCONFIG )


set_property(GLOBAL PROPERTY USE_FOLDERS ON)
set_property(TARGET AppGdmpLab PROPERTY FOLDER "GdmpLab")
set_property(TARGET AppGdmpLabImpl PROPERTY FOLDER "GdmpLab")
set_property(TARGET UiPlatform PROPERTY FOLDER "GdmpLab//Interaction")
set_property(TARGET UiConfiguration PROPERTY FOLDER "GdmpLab//Gui")
set_property(TARGET QtGuiImplementation PROPERTY FOLDER "GdmpLab//Gui")
set_property(TARGET UiCommonComponent PROPERTY FOLDER "GdmpLab//Gui")
set_property(TARGET GUiBaseImpl PROPERTY FOLDER "GdmpLab//Gui")
set_property(TARGET GuiMainFrame PROPERTY FOLDER "GdmpLab//Gui")
set_property(TARGET GuiQt PROPERTY FOLDER "GdmpLab//Gui")
set_property(TARGET UiQtImpl PROPERTY FOLDER "GdmpLab//Gui")
set_property(TARGET UiInplaceEdit PROPERTY FOLDER "GdmpLab//Interaction")
set_property(TARGET QtCommonWidget PROPERTY FOLDER "GdmpLab//Gui")
set_property(TARGET Model PROPERTY FOLDER "GdmpLab//Db")
set_property(TARGET ThirdPartyUpdate PROPERTY FOLDER "ThirdPartyUpdate")
set_property(DIRECTORY PROPERTY VS_STARTUP_PROJECT "AppGdmpLab")