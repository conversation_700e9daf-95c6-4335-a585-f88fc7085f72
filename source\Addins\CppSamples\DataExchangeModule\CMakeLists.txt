﻿include(GenerateExportHeader)

##设置target名称
set(TARGET_NAME DataExchangeModule)

##设置源代码目录
set(SOURCE_DIR ${CMAKE_CURRENT_SOURCE_DIR})
set(CMAKE_AUTOMOC ON)

# 提取所有的源文件(头文件和源文件分开提取)
file(GLOB_RECURSE  HEADER_FILES
    LIST_DIRECTORIES False  CONFIGURE_DEPENDS
    "${SOURCE_DIR}/*.h*"
)
file(GLOB_RECURSE  SRC_FILES
    LIST_DIRECTORIES False  CONFIGURE_DEPENDS
    "${SOURCE_DIR}/*.c*"
    "${SOURCE_DIR}/*.ui"
)

# 为VS设置源代码文件夹
source_group(
    TREE "${SOURCE_DIR}"
    PREFIX "Header Files"
    FILES ${HEADER_FILES}
)
source_group(
    TREE "${SOURCE_DIR}"
    PREFIX "Source Files"
    FILES ${SRC_FILES}
)

##界面资源
file(GLOB FORM_SOURCE_FILES *.ui) 
source_group("Forms" FILES ${FORM_SOURCE_FILES}) 

# 添加target及别名
add_library(${TARGET_NAME} SHARED)
set(CMAKE_PREFIX_PATH ${QTDIR_X64}) 
find_package(Qt5 COMPONENTS Gui Qml Quick Widgets REQUIRED)

file(GLOB file_ui "./*.ui")
QT5_WRAP_UI(ui_FILES ${file_ui} )

##界面转换
set_target_properties(${TARGET_NAME} PROPERTIES
    AUTOUIC ON 
)
set(CMAKE_AUTOUIC ON)

# 指定源文件
target_sources(${TARGET_NAME}
    PRIVATE  ${SRC_FILES}
    PRIVATE  ${HEADER_FILES}
#   PRIVATE  ${QRC_FILES}
)

# 设置预处理器定义
if(MSVC)
    target_compile_definitions(${TARGET_NAME}
        PRIVATE UNICODE NOMINMAX
    )
endif()

# 配置构建/使用时的头文件路径
target_include_directories(${TARGET_NAME}
	PUBLIC $<BUILD_INTERFACE:${SOURCE_DIR}>
	PUBLIC $<BUILD_INTERFACE:${SOURCE_DIR}>/GRep
	${GbmpThirdPart_pugixml_Include} 
	${GbmpThirdPart_jsoncpp_Include}
)

##设置所在文件夹
set_target_properties(${TARGET_NAME} PROPERTIES
    FOLDER "示例程序"
)

##设置库依赖
target_link_libraries(${TARGET_NAME}
			PRIVATE Gcmp::All
            Qt5::Gui
            Qt5::Qml
            Qt5::Quick 
            Qt5::Widgets
            PRIVATE SampleShared 
)

