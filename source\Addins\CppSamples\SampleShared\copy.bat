set SOURCEDIR=%~1
set SHARED_SOURCEDIR=%~2
set PLATFORM=%~3
set CONFIG=%~4
set OUTDIR=%~5
set IN_GDMPLAB=%~6

echo %PLATFORM%
echo %CONFIG%

:: pugixml
set source="%SOURCEDIR%\..\thirdparty\pugixml\lib\%PLATFORM%\%CONFIG%\pugixml.lib"
echo %source%
if exist "%SOURCEDIR%\..\thirdparty\pugixml\lib\%PLATFORM%\%CONFIG%\pugixml.lib" (
    xcopy /y /f "%SOURCEDIR%\..\thirdparty\pugixml\lib\%PLATFORM%\%CONFIG%\pugixml.lib" "%OUTDIR%"
) else (
    echo Warning: pugixml.lib not found at %source%
)

:: jsoncpp
set source="%SOURCEDIR%\..\thirdparty\jsoncpp\lib\%PLATFORM%\%CONFIG%\jsoncpp.lib"
echo %source%
if exist "%SOURCEDIR%\..\thirdparty\jsoncpp\lib\%PLATFORM%\%CONFIG%\jsoncpp.lib" (
    xcopy /y /f "%SOURCEDIR%\..\thirdparty\jsoncpp\lib\%PLATFORM%\%CONFIG%\jsoncpp.lib" "%OUTDIR%"
) else (
    echo Warning: jsoncpp.lib not found at %source%
)

if exist "%SHARED_SOURCEDIR%\..\sample_plugin_config\" (
    xcopy /y /s /e "%SHARED_SOURCEDIR%\..\sample_plugin_config\*" "%OUTDIR%\gbmp_plugin_config\"
) else (
    echo Warning: sample_plugin_config folder not found
)

if "%IN_GDMPLAB%"=="ON" (
    if exist "%SHARED_SOURCEDIR%\..\sample_plugin_config_ext\" (
        xcopy /y /s /e "%SHARED_SOURCEDIR%\..\sample_plugin_config_ext\*" "%OUTDIR%\gbmp_plugin_config\"
    ) else (
        echo Warning: sample_plugin_config_ext folder not found
    )
)

if not exist "%OUTDIR%\data" md "%OUTDIR%\data"
if not exist "%OUTDIR%\data\SampleData" md "%OUTDIR%\data\SampleData"
if exist "%SHARED_SOURCEDIR%\..\SampleData\" (
    xcopy /y /s /e "%SHARED_SOURCEDIR%\..\SampleData\*" "%OUTDIR%\data\SampleData\"
) else (
    echo Warning: SampleData folder not found
)