set(Structure ${CMAKE_CURRENT_SOURCE_DIR}/StructureModel) 
set(StructureFamily ${CMAKE_CURRENT_SOURCE_DIR}/StructureFamily) 
set(UiStructure ${CMAKE_CURRENT_SOURCE_DIR}/StructureUi) 

add_subdirectory(StructureModel)
add_subdirectory(StructureFamily)
add_subdirectory(StructureUi)

foreach( OUTPUTCONFIG ${CMAKE_CONFIGURATION_TYPES} ) 
string( TOUPPER ${OUTPUTCONFIG} OUTPUTCONFIG ) 

set_target_properties(Structure PROPERTIES   RUNTIME_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) )
set_target_properties(Structure PROPERTIES   LIBRARY_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) )
set_target_properties(Structure PROPERTIES   ARCHIVE_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) )

set_target_properties(StructureFamily PROPERTIES   RUNTIME_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) )
set_target_properties(StructureFamily PROPERTIES   LIBRARY_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) )
set_target_properties(StructureFamily PROPERTIES   ARCHIVE_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) )

set_target_properties(UiStructure PROPERTIES   RUNTIME_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) )
set_target_properties(UiStructure PROPERTIES   LIBRARY_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) )
set_target_properties(UiStructure PROPERTIES   ARCHIVE_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) )
endforeach( OUTPUTCONFIG ) 
 

set_property(TARGET Structure PROPERTY FOLDER "Addins//Structure") 
set_property(TARGET StructureFamily PROPERTY FOLDER "Addins//Structure") 
set_property(TARGET UiStructure PROPERTY FOLDER "Addins//Structure") 