set(GcmpPath "${GbmpThirdPart_GCMPSDK}")
include(CMakePrintHelpers)

if(NOT TARGET Gcmp::All)
    add_library(Gcmp::All IMPORTED SHARED GLOBAL)
    set_target_properties(Gcmp::All PROPERTIES
        #头文件路径
        INTERFACE_INCLUDE_DIRECTORIES "${GcmpPath}/include;${GcmpPath}/include/Gnuf"
        #导入库
        IMPORTED_IMPLIB "${GcmpPath}/lib/release/GcmpDevService.lib"
        #调试版本导入库
        IMPORTED_IMPLIB_DEBUG "${GcmpPath}/lib/debug/GcmpDevService.lib"        
        #输出文件位置
        IMPORTED_LOCATION "${GcmpPath}/bin/release/sdk/GcmpDevService.dll" 
        #调试版本输出文件位置
        IMPORTED_LOCATION_DEBUG "${GcmpPath}/bin/debug/sdk/GcmpDevService.dll"         
    )

    target_compile_definitions(Gcmp::All
        INTERFACE "_NOEXCEPT=noexcept"
    )

    #获取所有Release库并设置库依赖
    file(GLOB_RECURSE GCMPAllReleaseLibs
        LIST_DIRECTORIES False
        "${GcmpPath}/lib/release/*.lib"
    )
    list(REMOVE_ITEM GCMPAllReleaseLibs "${GcmpPath}/lib/release/cef_sandbox.lib")
    #cmake_print_variables(GCMPAllReleaseLibs)
    target_link_libraries(Gcmp::All INTERFACE
        "$<$<NOT:$<CONFIG:DEBUG>>:${GCMPAllReleaseLibs}>"
    )
    
    #获取所有debug库并设置库依赖
    file(GLOB_RECURSE GCMPAllDebugLibs
        LIST_DIRECTORIES False
        "${GcmpPath}/lib/debug/*.lib"
    )
    list(REMOVE_ITEM GCMPAllDebugLibs "${GcmpPath}/lib/debug/cef_sandbox.lib")
    #cmake_print_variables(GCMPAllDebugLibs)
    target_link_libraries(Gcmp::All INTERFACE
        "$<$<CONFIG:DEBUG>:${GCMPAllDebugLibs}>"
    )
endif()

set(GRepModule ${CMAKE_CURRENT_SOURCE_DIR}/GRepModule)
set(GeomModule ${CMAKE_CURRENT_SOURCE_DIR}/GeomModule)
set(PickSnapModule ${CMAKE_CURRENT_SOURCE_DIR}/PickSnapModule)
set(ViewModule ${CMAKE_CURRENT_SOURCE_DIR}/ViewModule) 
set(SampleShared ${CMAKE_CURRENT_SOURCE_DIR}/SampleShared) 
set(DataExchangeModule ${CMAKE_CURRENT_SOURCE_DIR}/DataExchangeModule) 
set(MiscModule ${CMAKE_CURRENT_SOURCE_DIR}/MiscModule)
set(ElemModule ${CMAKE_CURRENT_SOURCE_DIR}/ElemModule)

add_subdirectory(GRepModule) 
add_subdirectory(GeomModule) 
add_subdirectory(PickSnapModule) 
add_subdirectory(DataExchangeModule) 
add_subdirectory(ViewModule) 
add_subdirectory(SampleShared) 
add_subdirectory(MiscModule)
add_subdirectory(ElemModule) 
add_subdirectory(NDBModule) 

foreach( OUTPUTCONFIG ${CMAKE_CONFIGURATION_TYPES} ) 
string( TOUPPER ${OUTPUTCONFIG} OUTPUTCONFIG ) 
set_target_properties(GRepModule PROPERTIES   RUNTIME_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) ) 
set_target_properties(GRepModule PROPERTIES   LIBRARY_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) ) 
set_target_properties(GRepModule PROPERTIES   ARCHIVE_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) ) 
set_target_properties(GeomModule PROPERTIES   RUNTIME_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) ) 
set_target_properties(GeomModule PROPERTIES   LIBRARY_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) ) 
set_target_properties(GeomModule PROPERTIES   ARCHIVE_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) ) 
set_target_properties(PickSnapModule PROPERTIES   RUNTIME_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) ) 
set_target_properties(PickSnapModule PROPERTIES   LIBRARY_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) ) 
set_target_properties(PickSnapModule PROPERTIES   ARCHIVE_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) ) 
set_target_properties(DataExchangeModule PROPERTIES   RUNTIME_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) ) 
set_target_properties(DataExchangeModule PROPERTIES   LIBRARY_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) ) 
set_target_properties(DataExchangeModule PROPERTIES   ARCHIVE_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) ) 
set_target_properties(ViewModule PROPERTIES   RUNTIME_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) ) 
set_target_properties(ViewModule PROPERTIES   LIBRARY_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) ) 
set_target_properties(ViewModule PROPERTIES   ARCHIVE_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) ) 
set_target_properties(SampleShared PROPERTIES   RUNTIME_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) ) 
set_target_properties(SampleShared PROPERTIES   LIBRARY_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) ) 
set_target_properties(SampleShared PROPERTIES   ARCHIVE_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) ) 
set_target_properties(MiscModule PROPERTIES   RUNTIME_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) ) 
set_target_properties(MiscModule PROPERTIES   LIBRARY_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) ) 
set_target_properties(MiscModule PROPERTIES   ARCHIVE_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) ) 
set_target_properties(ElemModule PROPERTIES   RUNTIME_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) )
set_target_properties(ElemModule PROPERTIES   LIBRARY_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) )
set_target_properties(ElemModule PROPERTIES   ARCHIVE_OUTPUT_DIRECTORY_${OUTPUTCONFIG}  ${CMAKE_BINARY_DIR}/../$(Platform)$(Configuration) )
endforeach( OUTPUTCONFIG )

set_property(TARGET GRepModule PROPERTY FOLDER "Addins//CppSamples")
set_property(TARGET GeomModule PROPERTY FOLDER "Addins//CppSamples")
set_property(TARGET PickSnapModule PROPERTY FOLDER "Addins//CppSamples")
set_property(TARGET DataExchangeModule PROPERTY FOLDER "Addins//CppSamples")
set_property(TARGET ViewModule PROPERTY FOLDER "Addins//CppSamples")
set_property(TARGET SampleShared PROPERTY FOLDER "Addins//CppSamples")
set_property(TARGET MiscModule PROPERTY FOLDER "Addins//CppSamples")
set_property(TARGET ElemModule PROPERTY FOLDER "Addins//CppSamples")