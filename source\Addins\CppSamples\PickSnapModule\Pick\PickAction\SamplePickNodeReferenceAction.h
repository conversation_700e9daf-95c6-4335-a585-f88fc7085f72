﻿#pragma once

#include "GcmpCommandAction.h"
#include "IGraphicsNodeReference.h"
#include "PickNodeReferenceOption.h"
#include "IPickNodeReferenceAction.h"

#include "SamplePickActionBase.h"

class PickSketchObjectsFirstEventHandler;

namespace gcmp
{
    class IPickEventHandler;
    class IActionUiBehavior;
}

namespace Sample
{
///////////////////////////////////////////////////////////////////////////////////////////
    class SamplePickNodeReferenceAction : public SamplePickActionBase
    {
    public:
        SamplePickNodeReferenceAction(gcmp::PickNodeReferenceExchangeData& exchangeData,
            gcmp::IPickNodeReferenceAction::MoveCallback moveCallback=nullptr, bool bMultiSelect = false,
            gcmp::ActionFinishedMode finishedMode = gcmp::ActionFinishedMode::LButtonDown, bool isReturnKeyNeedCanceled = false);

        virtual ~SamplePickNodeReferenceAction();

        // IAction接口实现
    public:
        virtual void InitAction(gcmp::IUiView* pCurrentView) override;
        virtual bool OnLButtonDown(gcmp::IUiView* pCurrentView,const gcmp::Vector3d& pos) override;
        virtual bool OnMovePoint(gcmp::IUiView* pCurrentView, const gcmp::Vector3d& pos) override;
        virtual bool OnKeyDown(gcmp::IUiView* pCurrentView,int nChar) override;
        virtual bool OnLButtonUp(gcmp::IUiView* pCurrentView, const gcmp::Vector3d& pos) override;
        virtual bool OnRButtonDown(gcmp::IUiView* pCurrentView, const gcmp::Vector3d& pos) override;
        virtual bool OnRButtonUp(gcmp::IUiView* pCurrentView, const gcmp::Vector3d& pos) override;
        virtual gcmp::OwnerPtr<gcmp::IMenuItemContainerDefinition> PrepareContextMenu(gcmp::IUiView* pUIView) override;
        virtual void ActionCancelled() override;
        virtual void AddToSelection(gcmp::IDocument* pDocument, const gcmp::IGraphicsNodeReference& pickResult, const gcmp::IUiView* pCurrentView = nullptr) override;
        virtual void AddToSelectionGroup(gcmp::IDocument* pDoc, gcmp::GraphicsNodeReferenceOwnerPtrSet& pickResults) override;

        virtual void ClearSelection(gcmp::IDocument* pDoc) override;
        virtual bool IsSelectionEmpty() override;
        virtual void OnSelectionChanged() override;
        virtual std::wstring GetCursorPath() const override;

        //设置没有选中时结束命令还是继续拾取
        //isPickNullReturnPos为true的时候，没有拾取到就结束命令，并得到拾取到的点pickedPos
        //为false的时候，没有拾取到就继续拾取，直到拾取到才结束命令
        void SetPickNullReturnPos(bool isPickNullReturnPos, gcmp::Vector3d* pickedPos);

        void SetPickAuxiliaryElement(bool isPickAuxiliaryElement) { m_bPickElementShapeHandle = isPickAuxiliaryElement; }

        void SetPickFilter(gcmp::OwnerPtr<gcmp::IPickFilter> opPickFilter);
        void AddPostProcesserEventHandler(gcmp::IPickEventHandler* pPostEventHandler);
        bool IsInMultiSelectMode() { return m_bMultiSelectMode; }
        
        //设置键盘按钮按下时的回调函数
        virtual bool SetOnKeyDownCallback(gcmp::IPickNodeReferenceAction::OnKeyDownCallback callback);

        void SetActionUiBehavior(gcmp::OwnerPtr<gcmp::IActionUiBehavior> opActionUiBehavior);
    private:
        void GetCurrentMousePosition(gcmp::IUiView* pCurrentView, const gcmp::Vector3d& pos);
        void AddNodeReference(const gcmp::IGraphicsNodeReference* pickResult);
        void AddNodeReferenceGroup(gcmp::IDocument* pDoc,const gcmp::GraphicsNodeReferenceOwnerPtrSet& pickResults);
        void DeleteNodeReference(gcmp::IDocument* pDoc,const gcmp::GraphicsNodeReferenceOwnerPtrSet& pickResults);

        void OnDlgButtonClicked(bool isOKClicked);

    private:
        gcmp::PickNodeReferenceExchangeData m_pned;
        gcmp::IPickNodeReferenceAction::MoveCallback m_moveCallback;
        gcmp::IPickNodeReferenceAction::OnKeyDownCallback m_OnKeyDownCallback;
        //没有拾取到时是否结束命令标志
        bool m_isPickNullReturnPos;
        //没有拾取到的时候结束的话，返回的草图平面的点
        gcmp::Vector3d* m_pickedPos;
        bool m_bPickElementShapeHandle;
        //gcmp::OwnerPtr<gcmp::PickSketchObjectsFirstEventHandler> m_opPickSketchObjectsFirstEventHandler;
        bool m_bMultiSelectMode; //是否开启多次选择(true-可单次点选/框选或多次点选/框选；false-只能单次点选/框选)
        gcmp::ActionFinishedMode m_finishedMode;
        gcmp::OwnerPtr<gcmp::IActionUiBehavior> m_opActionUiBehavior;

    public:
        static gcmp::OwnerPtr<gcmp::IAction> Create(gcmp::PickNodeReferenceExchangeData& exchangeData,
            gcmp::OwnerPtr<gcmp::IPickFilter> opPickFilter,
            const gcmp::PickNodeReferenceOption& options = gcmp::PickNodeReferenceOption(),
            bool isReturnKeyNeedCanceled = false);
    };
}


